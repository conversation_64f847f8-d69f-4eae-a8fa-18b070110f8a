<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Calender_events
 *
 * <AUTHOR>
 */
class Calender_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SCHOOL_CALENDAR')) {
            redirect('dashboard', 'refresh');
        }
       // $this->lang->load('master_lang', 'english');
        $this->load->model('calenderevents_model');
        $this->load->helper('url');
        $this->load->helper('file');
        $this->load->library('acad_year');
    }

    public function index() {
        $data['date'] = date('Y-m');

        // Get calendar date range for the staff
        $calendar_date_range = $this->getStaffCalendarDateRange();
        $data['calendar_start_date'] = $calendar_date_range['start_date'];
        $data['calendar_end_date'] = $calendar_date_range['end_date'];

        if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'calenderEvents/staff_calendar_events';
        } else {
            /*$data['year'] = date('Y');
            $data['month'] = date('Y-m');
            $data['months'] = $this->_monthList();
            $data['main_content'] = 'calenderEvents/staff_calendar';*/
            $data['main_content'] = 'calenderEvents/staff_desktop_calendar';
        }
        $this->load->view('inc/template', $data);
    }

    private function _monthList() {
        $start_month = $this->settings->getSetting('academic_start_month');
        if($start_month == '') {
            $start_month = "June";
        }
        $start = date('Y-m', strtotime($start_month));
        $data = array();
        $year_month = date('Y-m', strtotime($start_month));
        $month = date('F', strtotime($year_month));
        for($i=1; $i<=12; $i++) {
            $data[$month] = $year_month;
            $year_month = date('Y-m', strtotime("+1 months", strtotime($year_month)));
            $month = date('F', strtotime($year_month));
        }
        // echo '<pre>'; print_r($data); die();
        return $data;
    }

    public function getEvents() {
        $date = $_POST['date'];
        $state = $_POST['state'];
        $student_board = isset($_POST['student_board'])?$_POST['student_board']:0;
        $applicable_to = $_POST['applicable_to'];
        if($state == 'next') {
            $date = date('Y-m', strtotime('+1 month', strtotime($date)));
        } else if($state == 'prev') {
            $date = date('Y-m', strtotime('-1 month', strtotime($date)));
        }
        // $staff = 1;
        $displayDate = date('M Y', strtotime($date));
        $eventData = $this->calenderevents_model->get_month_events($date, $applicable_to, $student_board);
        // echo "<pre>"; print_r($eventData);
        $arr = array();
        $i = 1;
        foreach ($eventData as $event) {
            $key = $event->fDate . $i ++;
            $eventOn = $event->fDay.' '.$event->fDate.' ';
            if($event->tDate != '' && $event->from_date!=$event->to_date) {
                $eventOn = $event->fMonth.' '. $event->fDate;//' ('.$event->fDay.')';
                $key .= '-'.$event->tDate;
                $eventOn .= '<br> to <br>'.$event->tMonth.' '.$event->tDate;//' ('.$event->tDay.')';
            }
            if(!array_key_exists($key, $arr)) {
                $arr[$key]['event_type'] = $event->event_type;
                $arr[$key]['event_on'] = $eventOn;
                $arr[$key]['board'] = array();
                $arr[$key]['names'] = array();
            }
            array_push($arr[$key]['names'], $event->event_name);
            $arr[$key]['board'][$event->event_name] = $event->board;
        }
        $events = array();
        foreach ($arr as $value) {
            array_push($events, $value);
        }
        // echo "<pre>"; print_r($events); die();
        echo json_encode(array('events'=>$events, 'displayDate'=>$displayDate, 'date'=>$date));
    }

    public function calenderEvents() {
        $applicable = $_POST['applicable'];
        $events = $this->calenderevents_model->get_events_all($applicable);

        $data_events = array();

        foreach($events as $r) {

            $background_color = "#e96d6d";
            if($r->event_type == 1 || $r->event_type == 4){
                $background_color = "#1caf9a";
            }else if($r->event_type == 5 || $r->event_type == 6){
                $background_color = "#ffb366";
            }
            $data_events[] = array(
                "id" => $r->id,
                "name" => $r->event_name,
                "enddate" => $r->to_date,
                "startdate" => $r->from_date,
                "color" => $background_color
            );
        }

        // echo json_encode(array("events" => $data_events));
        // exit();
        $folder = FCPATH.'assets/calendar/' . $this->settings->getSetting('school_short_name');
        $data = json_encode(array("monthly" => $data_events));
        // if (!file_exists($folder)) {
        //     mkdir($folder, 0777, true);
        // }
        if($applicable == 1){
            $file_name = $folder.'/staff_calendar_events.json';
        }
        else if($applicable == 2){
            $file_name = $folder.'/parent_calendar_events.json';
        }

        file_put_contents($file_name, $data);
    }

    /**
     * Staff Calendar V1 - Similar to parent school_calendar function
     */
    public function school_calendar()
    {
        if (!$this->authorization->isModuleEnabled('SCHOOL_CALENDAR')) {
            redirect('dashboard', 'refresh');
        }

        // Get calendar date range for the staff
        $calendar_date_range = $this->getStaffCalendarDateRange();
        $data['calendar_start_date'] = $calendar_date_range['start_date'];
        $data['calendar_end_date'] = $calendar_date_range['end_date'];

        if ($this->mobile_detect->isTablet()) {
            $data['date'] = date('Y-m');
            $data['main_content'] = 'calenderEvents/tablet_staff_calendar_events';
        } else if ($this->mobile_detect->isMobile()) {
            $data['date'] = date('Y-m');
            $data['main_content'] = 'calenderEvents/staff_calendar_events';
        } else {
            $data['year'] = date('Y');
            $data['month'] = date('Y-m');
            $data['months'] = $this->_monthList();
            $data['main_content'] = 'calenderEvents/staff_calendar';
        }

        $this->load->view('inc/template', $data);
    }

    /**
     * Staff Calendar V2 - Similar to parent school_calendar_v2 function
     */
    public function school_calendar_v2()
    {
        if (!$this->authorization->isModuleEnabled('CALENDAR_EVENTS_V2')) {
            redirect('dashboard', 'refresh');
        }

        // Get calendar date range for the staff
        $calendar_date_range = $this->getStaffCalendarDateRange();
        $data['calendar_start_date'] = $calendar_date_range['start_date'];
        $data['calendar_end_date'] = $calendar_date_range['end_date'];

        if ($this->mobile_detect->isTablet()) {
            $data['date'] = date('Y-m');
            $data['main_content'] = 'calenderEvents/staff_calendar_events_v2_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['date'] = date('Y-m');
            $data['main_content'] = 'calenderEvents/staff_calendar_events_v2_mobile';
        } else {
            $data['year'] = date('Y');
            $data['month'] = date('Y-m');
            $data['main_content'] = 'calenderEvents/staff_calendar_events_v2_desktop';
        }

        $this->load->view('inc/template', $data);
    }

    /**
     * Get calendar date range for staff
     * Similar to getStudentCalendarDateRange in Parent_controller
     */
    private function getStaffCalendarDateRange() {
        // Get current staff's staff type
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $staff_details = $this->db->select("staff_type")
            ->from("staff")
            ->where("id", $staff_id)
            ->get()->row();

        if (empty($staff_details)) {
            // Fallback to academic year if no staff details found
            $academic_start_month = (int)$this->settings->getSetting('academic_start_month');
            if($academic_start_month == '' || $academic_start_month > 12 || $academic_start_month < 1) {
                $academic_start_month = 3; // Default to March
            }
            $acad_year = explode("-", $this->acad_year->getAcadYear())[0];
            return array(
                'start_date' => $acad_year . '-' . sprintf('%02d', $academic_start_month) . '-01',
                'end_date' => ($acad_year + 1) . '-' . sprintf('%02d', $academic_start_month - 1) . '-28'
            );
        }

        // Get assigned calendar details for staff type
        $calendar = $this->db->select('cm.start_date, cm.end_date')
            ->from('calendar_v2_master cm')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = cm.id')
            ->where('cea.assigned_staff_type', $staff_details->staff_type)
            ->where('cea.assigned_type', 'STAFF')
            ->where('cm.academic_year', $this->acad_year->getAcadYearId())
            ->get()->row();

        if ($calendar) {
            return array(
                'start_date' => $calendar->start_date,
                'end_date' => $calendar->end_date
            );
        } else {
            // Fallback to academic year if no calendar assigned
            $academic_start_month = (int)$this->settings->getSetting('academic_start_month');
            if($academic_start_month == '' || $academic_start_month > 12 || $academic_start_month < 1) {
                $academic_start_month = 3; // Default to March
            }
            $acad_year = explode("-", $this->acad_year->getAcadYear())[0];
            return array(
                'start_date' => $acad_year . '-' . sprintf('%02d', $academic_start_month) . '-01',
                'end_date' => ($acad_year + 1) . '-' . sprintf('%02d', $academic_start_month - 1) . '-28'
            );
        }
    }
}