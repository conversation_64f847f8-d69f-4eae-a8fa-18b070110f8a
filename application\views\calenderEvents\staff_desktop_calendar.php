<?php 
    $colors = array(
        1 => 'color: #ce800dd6;font-style:normal;',
        2 => 'color: #ce800dd6;font-style:normal;',
        3 => 'color: #ce800dd6;font-style:normal;',
        4 => 'color: #ce800dd6;font-style:normal;',
        5 => 'color: #ce800dd6;font-style:normal;',
        6 => 'color:#118b9ac7;font-style:italic;',
    );
    $boards = $this->settings->getSetting('board');
?>
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Calendar Of Events</li>
</ul>

<hr>

<div class="col-md-12">
	<div class="panel panel-default new-panel-style_3">
		<div class="panel-body" style="padding:0px">
			<input type="hidden" name="selectedMonth" id="selectedMonth" value="<?php echo $date ?>">
			<div style="width:100%;">
				<div class="buttons" style="display: inline-block;width: 14%;padding: 2%;" onclick="getEvents('prev')">
					<span class="fa fa-arrow-circle-left" style="color:#00701a;font-size: 36px;padding:0px 30px;"></span>
	          		<span class="sr-only">Previous</span>
	          	</div>
				<div id="month" style="display: inline-block;width: 70%;text-align: center;font-weight: 700;font-size: 20px;"><?php echo date('M Y', strtotime($date)); ?></div>
				<div class="buttons" style="display: inline-block;width: 14%;padding: 2%;float: right;" onclick="getEvents('next')">
					<span class="fa fa-arrow-circle-right" style="color:#00701a;font-size: 36px;float: right;padding:0px 30px;"></span>
	          		<span class="sr-only">Next</span>
	      		</div>
			</div>
		</div>
		<div class="panel-body" style="border-top:1px solid black;padding:5px 5px 5px 20px;">
			<?php if(count($boards) > 1) {
		            foreach ($boards as $id => $name) {
		                echo '<p style="'.$colors[$id].'display: inline;"><span class="fa fa-square"></span>&nbsp;'.$name.'</p>&nbsp;&nbsp;';
		            }
	            }
	        ?>
		</div>
		<div class="panel-body" style="max-height: 600px;overflow-y: scroll;">
			<div class="profile-image" id="calendar-events">
				
			</div>
		</div>
	</div>
</div>

<div class="visible-xs visible-sm visible-md">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
	.event-tile{
		padding: 3px 5px;
		margin-bottom: 1%;
		min-height: 70px;
		border-radius: 5px;
	}
	.event-date{
		font-weight: 500;
		font-size: 15px;
	}
	.event-date>small {
		font-weight: 200;
	}
	.event-name {
		font-size: 12px;
		line-height: 17px;
	}
	.buttons:hover {
		background: #ccc;
	}
</style>

<script type="text/javascript">
	var colors = [];
	$(document).ready(function(){
		colors = JSON.parse('<?php echo json_encode($colors); ?>');
		getEvents('current');
	});

	function getEvents(state) {
		var cDate = $("#selectedMonth").val();

		// Use combined Calendar V1 + V2 endpoint
		$.ajax({
            url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/getEventsV2'); ?>',
            type: 'post',
            data: {'date':cDate,'state':state, 'applicable_to':1},
            success: function(data) {
                console.log('Combined Calendar response (Desktop):', data);
                try {
                    var parsedData = JSON.parse(data);
                    var events = parsedData.events;
                    var displayDate = parsedData.displayDate;
                    var date = parsedData.date;

                    $("#month").html(displayDate);
                    $("#selectedMonth").val(date);

                    if(events.length == 0) {
                    	$("#calendar-events").html('<h3 class="text-center">No Events</h3>' +
                    	    (parsedData.debug_info ? '<p style="font-size:12px;color:#666;">Source: ' + parsedData.debug_info.method + '</p>' : ''));
                    } else {
                    	var html = '';
                    	for (var i = 0; i < events.length; i++) {
                    		var border = 'border-left:5px solid #e96d6d;box-shadow:0px 0px 5px #e96d6d;';
                    		var eventType = '(Holiday)';
    						if(events[i].event_type == 1 || events[i].event_type == 4) {
    							eventType = '(Event)';
    							border = 'border-left:5px solid #00701a;box-shadow:0px 0px 5px #00701a;';
    						} else if(events[i].event_type == 5 || events[i].event_type == 6) {
    							eventType = '(Info)';
    							border = 'border-left:5px solid #ffb366;box-shadow:0px 0px 5px #1caf9a;';
    						}
    						html += '<div class="event-tile" style="'+border+'">';
    						html += '<p class="event-date">' +events[i].event_on+ '<small class="pull-right">'+eventType+'</small></p>';
    						var names = events[i].names;
    						var boards = events[i].board;
    						for (var j = 0; j < names.length; j++) {
    							html +='<p class="event-name" style="'+colors[boards[names[j]]]+';"><span style="font-weight:900;font-size:18px;">'+names[j].substring(0, 1)+'</span>'+ names[j].substring(1) +'</p>';
    						}
    						html += '</div>';
                    	}
                    	$("#calendar-events").html(html);
                    }
                } catch (e) {
                    console.error('Error parsing JSON (Desktop):', e);
                    console.log('Raw response (Desktop):', data);
                    // Fallback to Calendar V1 if combined endpoint fails
                    getEventsV1FallbackDesktop(cDate, state);
                }
            },
            error: function(xhr, status, error) {
                console.error('Combined Calendar AJAX Error (Desktop):', status, error);
                console.log('Response (Desktop):', xhr.responseText);
                // Fallback to Calendar V1 if combined endpoint fails
                getEventsV1FallbackDesktop(cDate, state);
            }
        });
	}

	// Fallback function for Calendar V1 only (Desktop)
	function getEventsV1FallbackDesktop(cDate, state) {
		console.log('Falling back to Calendar V1 only (Desktop)');
		$.ajax({
            url: '<?php echo site_url('calender_events/Calender_controller/getEvents'); ?>',
            type: 'post',
            data: {'date':cDate,'state':state, 'applicable_to':1},
            success: function(data) {
                console.log('Calendar V1 fallback response (Desktop):', data);
                var parsedData = JSON.parse(data);
                var events = parsedData.events;
                var displayDate = parsedData.displayDate;
                var date = parsedData.date;

                $("#month").html(displayDate);
                $("#selectedMonth").val(date);

                if(events.length == 0) {
                	$("#calendar-events").html('<h3 class="text-center">No Events</h3><p style="font-size:12px;color:#666;">Source: Calendar V1 (Fallback)</p>');
                } else {
                	var html = '';
                	for (var i = 0; i < events.length; i++) {
                		var border = 'border-left:5px solid #e96d6d;box-shadow:0px 0px 5px #e96d6d;';
                		var eventType = '(Holiday)';
						if(events[i].event_type == 1 || events[i].event_type == 4) {
							eventType = '(Event)';
							border = 'border-left:5px solid #00701a;box-shadow:0px 0px 5px #00701a;';
						} else if(events[i].event_type == 5 || events[i].event_type == 6) {
							eventType = '(Info)';
							border = 'border-left:5px solid #ffb366;box-shadow:0px 0px 5px #1caf9a;';
						}
						html += '<div class="event-tile" style="'+border+'">';
						html += '<p class="event-date">' +events[i].event_on+ '<small class="pull-right">'+eventType+'</small></p>';
						var names = events[i].names;
						var boards = events[i].board;
						for (var j = 0; j < names.length; j++) {
							html +='<p class="event-name" style="'+colors[boards[names[j]]]+';"><span style="font-weight:900;font-size:18px;">'+names[j].substring(0, 1)+'</span>'+ names[j].substring(1) +'</p>';
						}
						html += '</div>';
                	}
                	$("#calendar-events").html(html);
                }
            },
            error: function(xhr, status, error) {
                console.error('Calendar V1 fallback AJAX Error (Desktop):', status, error);
                $("#calendar-events").html('<h3 class="text-center">Error loading events</h3>');
            }
        });
	}
</script>